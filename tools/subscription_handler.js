import {ToolInterface} from './base.js';
import {log} from '../library/log.js';
import {l, lp} from "../library/translation.js";
import {ExternalUser} from '../models/associations.js';
import {getStorage} from '../library/storage.js';

class SubscriptionHandlerTool extends ToolInterface {

    onlyOwner = 'Only the bot owner can use this feature.';

    getDefinition() {
        // Пустой объект, так как мы не используем OpenAI для этого инструмента
        return {};
    }

    async execute(args) {
        // Пустая реализация, так как мы используем этот инструмент только для обработки событий
        return false;
    }

    getRegisteredEvents() {
        return [
            'menu_callback',       // для обработки меню
            'answer_received',      // для подсчета использованных сообщений
            'pre_checkout_query',   // для валидации оплаты
            'successful_payment',   // для обработки успешной оплаты
            'callback_query',       // для обработки нажатий на кнопки (для обратной совместимости)
            'message_received'      // для обработки ввода планов и пакетов
        ];
    }

    /**
     * Определяет пункты меню для управления подписками
     */
    getMenuItems() {
        return [
            // Единственный пункт в главном меню
            {
                id: 'subscription_management',
                text: '💳 Subscription management',
                callback_data: 'subscription_management_main',
                position: 'main',
                permissions: ['all'],
                order: 100
            },
            // Подменю для пользователей
            {
                id: 'subscription_plans',
                text: '📋 Available plans',
                callback_data: 'subscription_plans',
                position: 'subscription_menu',
                permissions: ['all'],
                order: 10
            },
            {
                id: 'subscription_boost',
                text: '🚀 Buy more messages',
                callback_data: 'subscription_boost',
                position: 'subscription_menu',
                permissions: ['all'],
                order: 20
            },
            {
                id: 'subscription_stats',
                text: '📊 My subscription',
                callback_data: 'subscription_stats',
                position: 'subscription_menu',
                permissions: ['all'],
                order: 30
            },
            // Подменю для администратора
            {
                id: 'subscription_admin',
                text: '⚙️ Admin settings',
                callback_data: 'subscription_admin',
                position: 'subscription_menu',
                permissions: ['owner'],
                order: 40
            },
            {
                id: 'subscription_admin_stats',
                text: '📈 Usage statistics',
                callback_data: 'subscription_admin_stats',
                position: 'subscription_admin_menu',
                permissions: ['owner'],
                order: 10
            },
            {
                id: 'subscription_admin_setup',
                text: '⚙️ Configure plans',
                callback_data: 'subscription_admin_setup',
                position: 'subscription_admin_menu',
                permissions: ['owner'],
                order: 20
            },
            {
                id: 'subscription_manual_assignment',
                text: '👤 Manual Assignment',
                callback_data: 'subscription_manual_assignment',
                position: 'subscription_admin_menu',
                permissions: ['owner'],
                order: 30
            },
            {
                id: 'subscription_back_to_main',
                text: '⬅️ Back to main menu',
                callback_data: 'subscription_back_to_main',
                position: 'subscription_menu',
                permissions: ['all'],
                order: 1000
            },
            {
                id: 'subscription_back_to_subscription',
                text: '⬅️ Back to subscription menu',
                callback_data: 'subscription_back_to_subscription',
                position: 'subscription_admin_menu',
                permissions: ['owner'],
                order: 1000
            }
        ];
    }

    async handleEvent(eventName, eventData, context) {
        try {
            switch (eventName) {
                case 'menu_callback':
                    // Обработка меню
                    return await this.handleMenuCallback(eventData, context);

                case 'answer_received':
                    // Увеличение счетчика использованных сообщений и проверка лимитов
                    return await this.countMessage(eventData, context);

                case 'pre_checkout_query':
                    // Валидация покупки
                    return await this.validatePurchase(eventData, context);

                case 'successful_payment':
                    // Активация подписки или пакета сообщений
                    return await this.processPurchase(eventData, context);

                case 'callback_query':
                    // Обработка нажатий на кнопки (для обратной совместимости)
                    return await this.handleCallbackQuery(eventData, context);

                case 'message_received':
                    // Обработка ввода планов и пакетов
                    return await this.handleMessageReceived(eventData, context);
            }

            return false;
        } catch (error) {
            log.error('subscription handler error', {
                error: error.message,
                eventName: eventName
            }, 'handleEvent', 'SubscriptionHandlerTool');
            return false;
        }
    }

    /**
     * Обработка событий меню
     */
    async handleMenuCallback(eventData, context) {
        const {menuItem} = eventData;
        const callbackData = menuItem.callback_data;

        // Главное меню подписок
        if (callbackData === 'subscription_management_main') {
            return await this.showSubscriptionMenu(context);
        }

        // Доступные планы
        if (callbackData === 'subscription_plans') {
            return await this.showAvailablePlans(context);
        }

        // Покупка дополнительных сообщений
        if (callbackData === 'subscription_boost') {
            return await this.showMessagePackages(context);
        }

        // Статистика подписки
        if (callbackData === 'subscription_stats') {
            return await this.showSubscriptionStats(context);
        }

        // Админ-меню
        if (callbackData === 'subscription_admin') {
            if (!context.isBotOwner) {
                await context.client.sendMessage(context.chatId, await l(context.user, this.onlyOwner), {}, true);
                return true;
            }
            return await this.showAdminMenu(context);
        }

        // Админ-статистика
        if (callbackData === 'subscription_admin_stats') {
            if (!context.isBotOwner) {
                await context.client.sendMessage(context.chatId, await l(context.user, this.onlyOwner), {}, true);
                return true;
            }
            return await this.showAdminStats(context);
        }

        // Настройка планов
        if (callbackData === 'subscription_admin_setup') {
            if (!context.isBotOwner) {
                await context.client.sendMessage(context.chatId, await l(context.user, this.onlyOwner), {}, true);
                return true;
            }
            return await this.setupSubscriptionPlans(context);
        }

        // Manual subscription assignment
        if (callbackData === 'subscription_manual_assignment') {
            if (!context.isBotOwner) {
                await context.client.sendMessage(context.chatId, await l(context.user, this.onlyOwner), {}, true);
                return true;
            }
            return await this.showUserSelectionMenu(context);
        }

        // Возврат в главное меню
        if (callbackData === 'subscription_back_to_main') {
            await context.client.showMainMenu(context.chatId, context.user);
            return true;
        }

        // Возврат в меню подписок
        if (callbackData === 'subscription_back_to_subscription') {
            return await this.showSubscriptionMenu(context);
        }

        return false;
    }

    /**
     * Показать меню подписок
     */
    async showSubscriptionMenu(context) {
        const {client, chatId, user} = context;

        // Получаем пункты меню для позиции subscription_menu
        // Get all menu items for the main position
        const permissions = [];

        // Add appropriate permissions based on user
        if (this.context.isBotOwner) {
            permissions.push('owner');
        }
        permissions.push('all');
        const menuItems = await client.getMenuItems('subscription_menu', permissions);
        const menuTitle = await l(user, "Subscription Management");

        // Показываем меню
        await client.sendMenu(chatId, menuTitle, menuItems);
        return true;
    }

    /**
     * Показать админ-меню
     */
    async showAdminMenu(context) {
        const {client, chatId, user} = context;

        // Получаем пункты меню для позиции subscription_admin_menu
        const menuItems = await client.getMenuItems('subscription_admin_menu', ['owner']);
        const menuTitle = await l(user, "Subscription Admin Settings");

        // Показываем меню
        await client.sendMenu(chatId, menuTitle, menuItems);
        return true;
    }

    /**
     * Обработка полученных сообщений для добавления планов и пакетов
     */
    async handleMessageReceived(eventData, context) {
        const userStorage = context.userStorage;

        // Проверяем, что пользователь является владельцем бота
        if (!context.isBotOwner) {
            return false;
        }

        // Проверяем состояние пользователя
        const addPlanState = await userStorage.get('subscription.addPlanState', false);
        const addPackageState = await userStorage.get('subscription.addPackageState', false);

        if (addPlanState) {
            // Сбрасываем состояние
            await userStorage.set('subscription.addPlanState', false);
            return await this.handleAddPlan(eventData.text, context);
        }

        if (addPackageState) {
            // Сбрасываем состояние
            await userStorage.set('subscription.addPackageState', false);
            return await this.handleAddPackage(eventData.text, context);
        }

        return false;
    }

    // Методы для обработки добавления планов и пакетов сохраняем для обратной совместимости
    // с существующими callback_query
    async handleAddPlanRequest(context) {
        try {
            if (!context.isBotOwner) {
                log.warn('not owner', {
                    botId: context.bot.id
                }, 'handleAddPlanRequest', 'SubscriptionHandlerTool');
                return false;
            }
            log.info('handleAddPlanRequest', {
                botId: context.bot.id
            }, 'handleAddPlanRequest', 'SubscriptionHandlerTool');

            const chatId = context.chatId;
            const client = context.client;
            const user = context.user;
            const userStorage = context.userStorage;

            // Устанавливаем состояние добавления плана
            await userStorage.set('subscription.addPlanState', true);

            await client.sendMessage(chatId,
                await l(user, "To add a plan, send a message in the format:\n\n" +
                    "ID|Name|Description|Price|Limit|Duration\n\n" +
                    "For example:\ndemo|Demo|Free plan for trial|0|10|7"), {}, true);
        } catch (e) {
            log.error('Error handling add plan request', {
                error: e.message,
                botId: context.bot.id
            }, 'handleAddPlanRequest', 'SubscriptionHandlerTool');
        }
        return true;
    }

    async handleAddPackageRequest(context) {
        try {
            if (!context.isBotOwner) {
                log.warn('not owner', {
                    botId: context.bot.id
                }, 'handleAddPackageRequest', 'SubscriptionHandlerTool');
                return false;
            }
            log.info('handleAddPackageRequest', {
                botId: context.bot.id
            }, 'handleAddPackageRequest', 'SubscriptionHandlerTool');
            const chatId = context.chatId;
            const client = context.client;
            const user = context.user;
            const userStorage = context.userStorage;

            // Устанавливаем состояние добавления пакета
            await userStorage.set('subscription.addPackageState', true);

            await client.sendMessage(chatId,
                await l(user, "To add a message package, send a message in the format:\n\n" +
                    "ID|Name|Description|Price|Count\n\n" +
                    "For example:\nboost10|Boost 10|10 additional messages for today|5|10"), {}, true);
        } catch (e) {
            log.error('Error handling add package request', {
                error: e.message,
                botId: context.bot.id
            }, 'handleAddPackageRequest', 'SubscriptionHandlerTool');
        }
        return true; //anyway
    }

    async handleAddPlan(message, context) {
        try {
            if (!context.isBotOwner) {
                log.warn('not owner', {
                    botId: context.bot.id
                }, 'handleAddPlan', 'SubscriptionHandlerTool');
                return false;
            }
            log.info('handleAddPlan', {
                botId: context.bot.id
            }, 'handleAddPlan', 'SubscriptionHandlerTool');
            const chatId = context.chatId;
            const client = context.client;
            const user = context.user;
            const botStorage = context.botStorage;

            // Парсим текст сообщения
            const planParts = message.split('|').map(part => part.trim());

            if (planParts.length < 6) {
                await client.sendMessage(chatId,
                    await l(user, "Invalid format. Use:\nID|Name|Description|Price|Limit|Duration"), {}, true);
                return true;
            }

            const [id, name, description, priceStr, limitStr, durationStr] = planParts;

            // Проверяем корректность числовых значений
            const price = parseInt(priceStr);
            const messageLimit = parseInt(limitStr);
            const duration = parseInt(durationStr);

            if (isNaN(price) || isNaN(messageLimit) || isNaN(duration) || messageLimit <= 0 || duration <= 0 || price < 0) {
                await client.sendMessage(chatId,
                    await l(user, "Invalid numerical values. Price, limit, and duration must be numbers."), {}, true);
                return true;
            }

            // Получаем текущие планы
            const plans = await botStorage.get('subscription.plans', []);

            if (plans.some(p => p.id === id)) {
                let hint = `full sentence for context: Plan with ID "${id}" already exists. Use another ID. "Plan" is subscription plan`;
                await client.sendMessage(chatId,
                    await l(user, `Plan with ID`, hint) +
                    +` ${id} ` +
                    await l(user, `already exists. Use another ID.`, hint), {}, true);
                return true;
            }

            // Создаем новый план
            const newPlan = {
                id,
                name,
                description,
                price,
                messageLimit,
                duration,
                isFree: price === 0
            };

            // Добавляем план
            plans.push(newPlan);
            await botStorage.set('subscription.plans', plans);

            // Если это первый план, автоматически включаем систему подписок
            if (plans.length === 1) {
                await botStorage.set('subscription.enabled', true);
            }

            await client.sendMessage(chatId, await l(user, "Plan") + ` "${name}" ` + await l(user, "has been successfully added."), {}, true);

            // Обновляем сообщение с настройками
            await this.setupSubscriptionPlans(context);
        } catch (e) {
            log.error('Error handling add plan', {
                error: e.message,
                botId: context.bot.id
            }, 'handleAddPlan', 'SubscriptionHandlerTool');
        }
        return true;
    }

    /**
     * Выбор плана подписки
     */
    async selectPlan(planId, context) {
        const chatId = context.chatId;
        const client = context.client;
        const user = context.user;
        const botStorage = context.botStorage;

        const plans = await botStorage.get('subscription.plans', []);
        const plan = plans.find(p => p.id === planId);

        if (!plan) {
            await client.sendMessage(chatId, await l(user, "Plan not found. Please try again."), {}, true);
            return true;
        }

        // Создаем счет на оплату через Telegram Payments
        try {
            await client.sendInvoice(
                chatId,
                plan.name,
                plan.description,
                JSON.stringify({type: 'plan', id: plan.id}),
                '', // provider_token
                'XTR', // currency
                [{label: plan.name, amount: plan.price}],
                {   //options
                    start_parameter: `pay_plan_${plan.id}`
                }
            );
        } catch (error) {
            log.error('Error sending invoice', {
                error: error.message,
                plan: plan.id
            }, 'selectPlan', 'SubscriptionHandlerTool');

            await client.sendMessage(chatId, await l(user, "An error occurred while creating the invoice. Please try again later."), {}, true);
        }

        return true;
    }

    /**
     * Выбор пакета сообщений
     */
    async selectPackage(packageId, context) {
        const chatId = context.chatId;
        const client = context.client;
        const user = context.user;
        const botStorage = context.botStorage;

        const packages = await botStorage.get('subscription.messagePackages', []);
        const pkg = packages.find(p => p.id === packageId);

        if (!pkg) {
            await client.sendMessage(chatId, await l(user, "Package not found. Please try again."), {}, true);
            return true;
        }

        // Создаем счет на оплату через Telegram Payments
        try {
            await client.sendInvoice(
                chatId,
                pkg.name,
                pkg.description,
                JSON.stringify({type: 'package', id: pkg.id}),
                '', // provider_token
                'XTR', // currency
                [{label: pkg.name, amount: pkg.price}],
                {   //options
                    start_parameter: `pay_package_${pkg.id}`
                }
            );
        } catch (error) {
            log.error('Error sending invoice', {
                error: error.message,
                plan: pkg.id
            }, 'selectPackage', 'SubscriptionHandlerTool');

            await client.sendMessage(chatId, await l(user, "An error occurred while creating the invoice. Please try again later."), {}, true);
        }

        return true;
    }

    async handleAddPackage(message, context) {
        const chatId = context.chatId;
        const client = context.client;
        const user = context.user;
        const botStorage = context.botStorage;

        // Парсим текст сообщения
        const packageParts = message.split('|').map(part => part.trim());

        if (packageParts.length < 5) {
            await client.sendMessage(chatId,
                await l(user, "Invalid format. Use:\nID|Name|Description|Price|Count"), {}, true);
            return true;
        }

        const [id, name, description, priceStr, countStr] = packageParts;

        // Проверяем корректность числовых значений
        const price = parseInt(priceStr);
        const messageCount = parseInt(countStr);

        if (isNaN(price) || isNaN(messageCount) || messageCount <= 0 || price <= 0) {
            await client.sendMessage(chatId,
                await l(user, "Invalid numerical values. Price and count must be numbers."), {}, true);
            return true;
        }

        // Получаем текущие пакеты
        const packages = await botStorage.get('subscription.messagePackages', []);

        // Проверяем уникальность ID
        if (packages.some(p => p.id === id)) {
            let hint = `full sentence for context: Package with ID "${id}" already exists. Use another ID. "Package" is service package`;
            await client.sendMessage(chatId,
                await l(user, `Package with ID`, hint) +
                +` ${id} ` +
                await l(user, `already exists. Use another ID.`, hint), {}, true);
            return true;
        }

        // Создаем новый пакет
        const newPackage = {
            id,
            name,
            description,
            price,
            messageCount
        };

        // Добавляем пакет
        packages.push(newPackage);
        await botStorage.set('subscription.messagePackages', packages);

        await client.sendMessage(chatId, await l(user, "Package") + ` "${name}" ` + await l(user, "has been successfully added."), {}, true);

        // Обновляем сообщение с настройками
        await this.setupSubscriptionPlans(context);
        return true;
    }

    async handleCallbackQuery(callbackQuery, context) {
        const data = callbackQuery.data;
        const chatId = callbackQuery.message.chat.id;
        const client = context.client;
        const user = context.user;
        const botStorage = context.botStorage;

        // Обработка выбора плана
        if (data.startsWith('select_plan:')) {
            const planId = data.split(':')[1];
            return await this.selectPlan(planId, context);
        }

        // Обработка выбора пакета сообщений
        if (data.startsWith('select_package:')) {
            const packageId = data.split(':')[1];
            return await this.selectPackage(packageId, context);
        }


        // Включение/отключение системы подписок
        if (data === 'subscription_toggle') {
            const subscriptionEnabled = await botStorage.get('subscription.enabled', false);
            await botStorage.set('subscription.enabled', !subscriptionEnabled);

            await client.sendMessage(chatId,
                await l(user, `Subscription system is ${!subscriptionEnabled ? 'enabled' : 'disabled'}.`), {}, true);

            // Обновляем сообщение с настройками
            await this.setupSubscriptionPlans(context);
            return true;
        }

        // Добавление нового плана
        if (data === 'subscription_add_plan') {
            return await this.handleAddPlanRequest(context);
        }

        // Удаление плана
        if (data === 'subscription_remove_plan') {
            const plans = await botStorage.get('subscription.plans', []);

            if (plans.length === 0) {
                await client.sendMessage(chatId, await l(user, "No available plans to delete."), {}, true);
                return true;
            }

            // Создаем клавиатуру с доступными планами
            const inlineKeyboard = plans.map(plan => [{
                text: `${plan.name} (${plan.price} звезд)`,
                callback_data: `subscription_remove_plan:${plan.id}`
            }]);

            await client.sendMessage(chatId,
                await l(user, "Select a plan to delete:"), {
                    reply_markup: {
                        inline_keyboard: inlineKeyboard
                    }
                }, true);

            return true;
        }

        // Подтверждение удаления плана
        if (data.startsWith('subscription_remove_plan:')) {
            const planId = data.split(':')[1];
            const plans = await botStorage.get('subscription.plans', []);
            const planIndex = plans.findIndex(p => p.id === planId);

            if (planIndex === -1) {
                await client.sendMessage(chatId, await l(user, "Plan not found."), {}, true);
                return true;
            }

            // Удаляем план
            const removedPlan = plans.splice(planIndex, 1)[0];
            await botStorage.set('subscription.plans', plans);

            await client.sendMessage(chatId, await l(user, "Plan") + ` "${removedPlan.name}" ` + await l(user, "has been successfully deleted."), {}, true);

            // Обновляем сообщение с настройками
            await this.setupSubscriptionPlans(context);
            return true;
        }

        // Добавление нового пакета сообщений
        if (data === 'subscription_add_package') {
            return await this.handleAddPackageRequest(context);
        }

        // Удаление пакета сообщений
        if (data === 'subscription_remove_package') {
            const packages = await botStorage.get('subscription.messagePackages', []);

            if (packages.length === 0) {
                await client.sendMessage(chatId, await l(user, "No available packages to delete."), {}, true);
                return true;
            }

            // Создаем клавиатуру с доступными пакетами
            const inlineKeyboard = packages.map(pkg => [{
                text: `${pkg.name} (${pkg.price} звезд)`,
                callback_data: `subscription_remove_package:${pkg.id}`
            }]);

            await client.sendMessage(chatId,
                await l(user, "Select a package to delete:"), {
                    reply_markup: {
                        inline_keyboard: inlineKeyboard
                    }
                }, true);

            return true;
        }

        // Подтверждение удаления пакета
        if (data.startsWith('subscription_remove_package:')) {
            const packageId = data.split(':')[1];
            const packages = await botStorage.get('subscription.messagePackages', []);
            const packageIndex = packages.findIndex(p => p.id === packageId);

            if (packageIndex === -1) {
                await client.sendMessage(chatId, await l(user, "Package not found. Please try again."));
                return true;
            }

            // Удаляем пакет
            const removedPackage = packages.splice(packageIndex, 1)[0];
            await botStorage.set('subscription.messagePackages', packages);

            await client.sendMessage(chatId, await l(user, "Package") + ` "${removedPackage.name}" ` + await l(user, "has been successfully deleted."), {}, true);

            // Обновляем сообщение с настройками
            await this.setupSubscriptionPlans(context);
            return true;
        }

        // Manual assignment callbacks
        if (!context.isBotOwner) {
            return false; // Only owner can use manual assignment
        }

        // User selection for manual assignment
        if (data.startsWith('sub_assign_user_')) {
            const userId = data.replace('sub_assign_user_', '');
            return await this.showPlanSelectionMenu(context, userId);
        }

        // Plan selection for manual assignment
        if (data.startsWith('sub_assign_plan_')) {
            const parts = data.replace('sub_assign_plan_', '').split('_');
            const planId = parts[0];
            const userId = parts[1];
            return await this.assignSubscriptionToUser(context, userId, planId);
        }

        // Pagination for user selection
        if (data.startsWith('sub_assign_prev_')) {
            const offset = parseInt(data.replace('sub_assign_prev_', ''));
            return await this.showUserSelectionMenu(context, offset);
        }

        if (data.startsWith('sub_assign_next_')) {
            const offset = parseInt(data.replace('sub_assign_next_', ''));
            return await this.showUserSelectionMenu(context, offset);
        }

        return false;
    }

    async countMessage(eventData, context) {
        const answer = eventData.answer;
        if (!answer || answer.err) {
            return false; // Не считаем ошибочные сообщения
        }
        if (this.context.isBotOwner) {
            return false;
        }

        const botStorage = context.botStorage;
        const userStorage = context.userStorage;

        // Проверяем, включена ли функция платных подписок
        const subscriptionEnabled = await botStorage.get('subscription.enabled', false);
        if (!subscriptionEnabled) {
            return false;
        }

        // Получаем информацию о подписке пользователя
        const userSubscription = await userStorage.get('subscription', null);
        if (!userSubscription?.planId) {
            // Если у пользователя нет подписки, проверяем бесплатный план
            const freePlan = await this.getFreePlan(botStorage);
            if (freePlan) {
                // Автоматически назначаем бесплатный план
                await this.assignFreePlan(userStorage, freePlan);
                return false;
            } else {
                // Если нет бесплатного плана, отправляем сообщение с предложением подписки
                await context.client.sendMessage(context.chatId,
                    await l(context.user, "To use the bot, please subscribe via the Subscription management menu"), {}, true);
                return true; // Блокируем сообщение
            }
        }

        // Проверяем, не истекла ли подписка
        const now = new Date();
        if (userSubscription.endDate && new Date(userSubscription.endDate) < now) {
            // Подписка истекла, проверяем бесплатный план
            const freePlan = await this.getFreePlan(botStorage);
            if (freePlan) {
                // Автоматически назначаем бесплатный план
                await this.assignFreePlan(userStorage, freePlan);
                await context.client.sendMessage(context.chatId,
                    await l(context.user, "Your subscription has expired. You have been switched to the free plan."), {}, true);
            } else {
                // Если нет бесплатного плана, отправляем сообщение с предложением подписки
                await context.client.sendMessage(context.chatId,
                    await l(context.user, "Your subscription has expired. To continue using the bot, please subscribe again via the Subscription management menu"), {}, true);
                return true; // Блокируем сообщение
            }
        }
        // Получаем текущую дату в формате YYYY-MM-DD
        const today = new Date().toISOString().split('T')[0];
        const lastUsedDate = userSubscription.lastUsedDate || '';

        // Проверяем, нужно ли сбросить счетчик для нового дня
        if (lastUsedDate !== today) {
            await userStorage.set('subscription.usedToday', 0);
            await userStorage.set('subscription.lastUsedDate', today);
            // Сбрасываем дополнительные сообщения (действуют только на текущий день)
            await userStorage.set('subscription.extraToday', 0);
        }

        // Получаем текущие показатели использования
        const usedToday = await userStorage.get('subscription.usedToday', 0);
        const dailyLimit = await userStorage.get('subscription.dailyLimit', 0);
        const bonusLimit = await userStorage.get('subscription.bonusLimit', 0);
        const extraToday = await userStorage.get('subscription.extraToday', 0);

        // Общий лимит на сегодня
        const totalLimit = dailyLimit + bonusLimit + extraToday;

        // Увеличиваем счетчик использованных сообщений
        const newUsedCount = usedToday + 1;
        await userStorage.set('subscription.usedToday', newUsedCount);

        // Отправляем уведомления о приближении к лимиту
        if (newUsedCount === Math.floor(totalLimit * 0.8) || newUsedCount === Math.floor(totalLimit * 0.95)) {
            await context.client.sendMessage(context.chatId,
                await lp(context.user, `{count} messages remaining for today.`, totalLimit - newUsedCount), {}, true);
        }

        // Проверяем, превышен ли лимит
        if (newUsedCount > totalLimit) {
            // Вычисляем, сколько времени осталось до сброса лимита (00:00 GMT)
            const hoursLeft = this.getHoursUntilReset();

            await context.client.sendMessage(context.chatId,
                await l(context.user, `Your message limit for today has been reached. Upgrade your subscription or buy more messages via the Subscription management menu.`) +
                await lp(context.user, `Next limit reset in {count} hours.`, hoursLeft), {}, true);

            // Обновляем статистику использования бота
            await this.updateBotStats(botStorage, 'limitExceeded');

            return true; // Блокируем сообщение
        }

        // Обновляем статистику использования бота
        await this.updateBotStats(botStorage, 'messageSent');

        return false; // Не блокируем сообщение
    }

    async validatePurchase(eventData, context) {
        // Обработка pre_checkout_query события
        const preCheckoutQuery = eventData;
        const payload = JSON.parse(preCheckoutQuery.invoice_payload);

        // Проверяем тип покупки и наличие соответствующего плана или пакета
        if (payload.type === 'plan') {
            const plans = await context.botStorage.get('subscription.plans', []);
            const plan = plans.find(p => p.id === payload.id);

            if (!plan) {
                // План не найден
                await context.client.answerPreCheckoutQuery(
                    preCheckoutQuery.id,
                    false,
                    await l(context.user, "Subscription plan not found. Please try again.")
                );
                return true;
            }
        } else if (payload.type === 'package') {
            const packages = await context.botStorage.get('subscription.messagePackages', []);
            const pkg = packages.find(p => p.id === payload.id);

            if (!pkg) {
                // Пакет не найден
                await context.client.answerPreCheckoutQuery(
                    preCheckoutQuery.id,
                    false,
                    await l(context.user, "Package not found. Please try again.")
                );
                return true;
            }
        } else {
            // Неизвестный тип покупки
            await context.client.answerPreCheckoutQuery(
                preCheckoutQuery.id,
                false,
                await l(context.user, "Unknown item. Please try again.")
            );
            return true;
        }

        // Все проверки пройдены, подтверждаем покупку
        await context.client.answerPreCheckoutQuery(preCheckoutQuery.id, true);
        return true;
    }

    async processPurchase(eventData, context) {
        // Обработка successful_payment события
        const payment = eventData;
        const payload = JSON.parse(payment.successful_payment.invoice_payload);
        const botStorage = context.botStorage;
        const userStorage = context.userStorage;

        if (payload.type === 'plan') {
            // Обработка покупки плана
            const plans = await botStorage.get('subscription.plans', []);
            const plan = plans.find(p => p.id === payload.id);

            if (!plan) {
                await context.client.sendMessage(context.chatId,
                    await l(context.user, "An error occurred while activating the plan. Please contact the administrator."), {}, true);
                return true;
            }

            // Получаем текущую подписку пользователя для возможного апгрейда
            const currentSubscription = await userStorage.get('subscription', null);

            // Устанавливаем даты начала и окончания подписки
            const startDate = new Date();
            const endDate = new Date();
            endDate.setDate(endDate.getDate() + plan.duration);

            // Создаем новую подписку
            const newSubscription = {
                planId: plan.id,
                planName: plan.name,
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
                dailyLimit: plan.messageLimit,
                bonusLimit: 0,
                usedToday: 0,
                extraToday: 0,
                lastUsedDate: new Date().toISOString().split('T')[0]
            };

            // Если это апгрейд с платного плана на более дорогой платный план
            if (currentSubscription &&
                currentSubscription.planId &&
                currentSubscription.endDate &&
                new Date(currentSubscription.endDate) > new Date()) {

                const oldPlan = plans.find(p => p.id === currentSubscription.planId);

                if (oldPlan && oldPlan.price > 0) {
                    // Расчет бонусного лимита от перехода на новый план
                    const remainingDays = Math.max(0, Math.floor((new Date(currentSubscription.endDate) - new Date()) / (1000 * 60 * 60 * 24)));
                    const totalRemainingMessages = (currentSubscription.dailyLimit + (currentSubscription.bonusLimit || 0)) * remainingDays;
                    newSubscription.bonusLimit = Math.ceil(totalRemainingMessages / plan.duration);
                }
            }

            // Сохраняем новую подписку
            await userStorage.set('subscription', newSubscription);

            // Обновляем статистику
            await this.updateSubscriptionStats(botStorage, plan.id, plan.price);

            // Отправляем сообщение об успешной активации плана
            const bonusText = newSubscription.bonusLimit > 0
                ? await lp(context.user, `\nUpgrade bonus: +{count} messages per day!`, newSubscription.bonusLimit)
                : '';

            await context.client.sendMessage(context.chatId,
                await l(context.user, `Plan`) + ` "${plan.name}" ` + await l(context.user, "has been successfully activated.") + `\n` +
                await lp(context.user, `Limit: {count} messages per day`, plan.messageLimit) + `\n` +
                await lp(context.user, `Period: {count} days`, plan.duration) +
                bonusText, {}, true);

            return true;
        } else if (payload.type === 'package') {
            // Обработка покупки пакета сообщений
            const packages = await botStorage.get('subscription.messagePackages', []);
            const pkg = packages.find(p => p.id === payload.id);

            if (!pkg) {
                await context.client.sendMessage(context.chatId,
                    await l(context.user, "An error occurred while activating the message package. Please contact the administrator."), {}, true);
                return true;
            }

            // Получаем текущую подписку пользователя
            let userSubscription = await userStorage.get('subscription', null);

            // Если у пользователя нет подписки, проверяем бесплатный план
            if (!userSubscription?.planId) {
                const freePlan = await this.getFreePlan(botStorage);
                if (freePlan) {
                    // Автоматически назначаем бесплатный план
                    await this.assignFreePlan(userStorage, freePlan);
                    userSubscription = await userStorage.get('subscription', null);
                } else {
                    await context.client.sendMessage(context.chatId,
                        await l(context.user, "To start, you need to activate a subscription plan via /pay"), {}, true);
                    return true;
                }
            }

            // Добавляем дополнительные сообщения на сегодня
            const extraToday = (userSubscription.extraToday || 0) + pkg.messageCount;
            await userStorage.set('subscription.extraToday', extraToday);

            // Обновляем статистику
            await this.updatePackageStats(botStorage, pkg.id, pkg.price);

            await context.client.sendMessage(context.chatId,
                await l(context.user, `Package`) + ` "${pkg.name}" ` + await l(context.user, "has been successfully activated.") + `\n` +
                await lp(context.user, `Added: {count} messages for today`, pkg.messageCount) + `\n` +
                await lp(context.user, `Total available today: {count} messages`, userSubscription.dailyLimit + (userSubscription.bonusLimit || 0) + extraToday), {}, true);

            return true;
        }

        return false;
    }

    // Получение бесплатного плана (если он существует)
    async getFreePlan(botStorage) {
        const plans = await botStorage.get('subscription.plans', []);
        return plans.find(plan => plan.price === 0);
    }

    // Назначение бесплатного плана пользователю
    async assignFreePlan(userStorage, freePlan) {
        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(endDate.getDate() + freePlan.duration);

        const subscription = {
            planId: freePlan.id,
            planName: freePlan.name,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            dailyLimit: freePlan.messageLimit,
            bonusLimit: 0,
            usedToday: 0,
            extraToday: 0,
            lastUsedDate: new Date().toISOString().split('T')[0]
        };

        await userStorage.set('subscription', subscription);
    }

    // Обновление статистики подписок
    async updateSubscriptionStats(botStorage, planId, price) {
        const today = new Date().toISOString().split('T')[0];
        const currentMonth = today.substring(0, 7); // YYYY-MM
        const currentYear = today.substring(0, 4);  // YYYY

        // Обновляем счетчик активных подписок по плану
        const activePlansCount = await botStorage.get(`subscription.stats.activePlans.${planId}`, 0);
        await botStorage.set(`subscription.stats.activePlans.${planId}`, activePlansCount + 1);

        // Обновляем доход за день/месяц/год
        if (price > 0) {
            const dailyIncome = await botStorage.get(`subscription.stats.income.daily.${today}`, 0);
            await botStorage.set(`subscription.stats.income.daily.${today}`, dailyIncome + price);

            const monthlyIncome = await botStorage.get(`subscription.stats.income.monthly.${currentMonth}`, 0);
            await botStorage.set(`subscription.stats.income.monthly.${currentMonth}`, monthlyIncome + price);

            const yearlyIncome = await botStorage.get(`subscription.stats.income.yearly.${currentYear}`, 0);
            await botStorage.set(`subscription.stats.income.yearly.${currentYear}`, yearlyIncome + price);
        }
    }

    // Обновление статистики покупки пакетов
    async updatePackageStats(botStorage, packageId, price) {
        const today = new Date().toISOString().split('T')[0];
        const currentMonth = today.substring(0, 7); // YYYY-MM
        const currentYear = today.substring(0, 4);  // YYYY

        // Обновляем счетчик купленных пакетов
        const dailyPackages = await botStorage.get(`subscription.stats.packages.daily.${today}.${packageId}`, 0);
        await botStorage.set(`subscription.stats.packages.daily.${today}.${packageId}`, dailyPackages + 1);

        const monthlyPackages = await botStorage.get(`subscription.stats.packages.monthly.${currentMonth}.${packageId}`, 0);
        await botStorage.set(`subscription.stats.packages.monthly.${currentMonth}.${packageId}`, monthlyPackages + 1);

        const yearlyPackages = await botStorage.get(`subscription.stats.packages.yearly.${currentYear}.${packageId}`, 0);
        await botStorage.set(`subscription.stats.packages.yearly.${currentYear}.${packageId}`, yearlyPackages + 1);

        // Обновляем доход
        if (price > 0) {
            const dailyIncome = await botStorage.get(`subscription.stats.income.daily.${today}`, 0);
            await botStorage.set(`subscription.stats.income.daily.${today}`, dailyIncome + price);

            const monthlyIncome = await botStorage.get(`subscription.stats.income.monthly.${currentMonth}`, 0);
            await botStorage.set(`subscription.stats.income.monthly.${currentMonth}`, monthlyIncome + price);

            const yearlyIncome = await botStorage.get(`subscription.stats.income.yearly.${currentYear}`, 0);
            await botStorage.set(`subscription.stats.income.yearly.${currentYear}`, yearlyIncome + price);
        }
    }

    // Обновление статистики использования бота
    async updateBotStats(botStorage, actionType) {
        const today = new Date().toISOString().split('T')[0];

        if (actionType === 'messageSent') {
            const messagesCount = await botStorage.get(`subscription.stats.usage.messages.${today}`, 0);
            await botStorage.set(`subscription.stats.usage.messages.${today}`, messagesCount + 1);
        } else if (actionType === 'limitExceeded') {
            const limitExceededCount = await botStorage.get(`subscription.stats.usage.limitExceeded.${today}`, 0);
            await botStorage.set(`subscription.stats.usage.limitExceeded.${today}`, limitExceededCount + 1);
        }
    }

    // Показать доступные планы подписки
    async showAvailablePlans(context) {
        const chatId = context.chatId;
        const client = context.client;
        const user = context.user;
        const botStorage = context.botStorage;

        // Получаем все платные планы
        const plans = await botStorage.get('subscription.plans', []);
        const paidPlans = plans.filter(plan => plan.price > 0);

        if (paidPlans.length === 0) {
            await client.sendMessage(chatId, await l(user, "There are currently no available paid plans."), {}, true);
            return true;
        }

        let message = await l(user, "Available subscription plans:") + "\n\n";

        // Если план только один, показываем кнопку оплаты сразу
        if (paidPlans.length === 1) {
            const plan = paidPlans[0];
            message += `📌 <b>${plan.name}</b>\n`;
            message += `📝 ${plan.description}\n`;
            message += `📊 ${await l(user, `Limit:`) + ` ` + await lp(user, `{count} messages per day`, plan.messageLimit)}\n`;
            message += `⏱ ${await l(user, `Duration:`) + ` ` + await lp(user, `{count} days`, plan.duration)}\n`;
            message += `💰 ${await l(user, `Price:`) + ` ` + await lp(user, `{count} stars`, plan.price)}\n\n`;

            await client.sendMessage(chatId, message, {}, true);
            // Создаем счет на оплату через Telegram Payments
            try {
                await client.sendInvoice(
                    chatId,
                    plan.name,
                    plan.description,
                    JSON.stringify({type: 'plan', id: plan.id}),
                    '', // provider_token
                    'XTR', // currency
                    [{label: plan.name, amount: plan.price}],
                    {   //options
                        start_parameter: `pay_plan_${plan.id}`
                    }
                );
            } catch (error) {
                log.error('Error sending invoice', {
                    error: error.message,
                    plan: plan.id
                }, 'showAvailablePlans', 'SubscriptionHandlerTool');

                await client.sendMessage(chatId, await l(user, "An error occurred while creating the invoice. Please try again later."), {}, true);
            }

        } else {
            // Показываем список планов с кнопками выбора
            for (const plan of paidPlans) {
                message += `📌 <b>${plan.name}</b>\n`;
                message += `📝 ${plan.description}\n`;
                message += `📊 ${await l(user, `Limit:`) + ` ` + await lp(user, `{count} messages per day`, plan.messageLimit)}\n`;
                message += `⏱ ${await l(user, `Duration:`) + ` ` + await lp(user, `{count} days`, plan.duration)}\n`;
                message += `💰 ${await l(user, `Price:`) + ` ` + await lp(user, `{count} stars`, plan.price)}\n\n`;
            }

            const buttonPromises = paidPlans.map(async plan => [{
                text: `${plan.name} - ${await lp(user, '{count} stars', plan.price)}`,
                callback_data: `select_plan:${plan.id}`
            }]);

            const inlineKeyboard = await Promise.all(buttonPromises);

            await client.sendMessage(chatId, message, {
                reply_markup: {
                    inline_keyboard: inlineKeyboard
                }
            }, true);
        }

        return true;
    }

    // Показать доступные пакеты дополнительных сообщений
    async showMessagePackages(context) {
        const chatId = context.chatId;
        const client = context.client;
        const user = context.user;
        const botStorage = context.botStorage;

        // Получаем все пакеты сообщений
        const packages = await botStorage.get('subscription.messagePackages', []);

        if (packages.length === 0) {
            await client.sendMessage(chatId, await l(user, "There are currently no available message packages."), {}, true);
            return true;
        }

        let message = await l(user, "Available additional message packages:\n\n");

        // Если пакет только один, показываем кнопку оплаты сразу
        if (packages.length === 1) {
            const pkg = packages[0];
            message += `📩 <b>${pkg.name}</b>\n`;
            message += `📝 ${pkg.description}\n`;
            message += `📊 ${await lp(user, `Amount: +{count} messages`, pkg.messageCount)}\n`;
            message += `💰 ${await lp(user, `Price: {count} stars`, pkg.price)}\n`;

            await client.sendMessage(chatId, message, {}, true);
            // Создаем счет на оплату через Telegram Payments
            try {
                await client.sendInvoice(
                    chatId,
                    pkg.name,
                    pkg.description,
                    JSON.stringify({type: 'package', id: pkg.id}),
                    '', // provider_token
                    'XTR', // currency
                    [{label: pkg.name, amount: pkg.price}],
                    {   //options
                        start_parameter: `pay_package_${pkg.id}`
                    }
                );
            } catch (error) {
                log.error('Error sending invoice', {
                    error: error.message,
                    plan: pkg.id
                }, 'showMessagePackages', 'SubscriptionHandlerTool');

                await client.sendMessage(chatId, await l(user, "An error occurred while creating the invoice. Please try again later."), {}, true);
            }
        } else {
            // Показываем список пакетов с кнопками выбора
            for (const pkg of packages) {
                message += `📩 <b>${pkg.name}</b>\n`;
                message += `📝 ${pkg.description}\n`;
                message += `📊 ${await lp(user, `Amount: +{count} messages`, pkg.messageCount)}\n`;
                message += `💰 ${await lp(user, `Price: {count} stars`, pkg.price)}\n`;
            }

            // Создаем кнопки выбора пакета
            const inlineKeyboard = packages.map(pkg => [{
                text: `${pkg.name} - ${pkg.price} звезд`,
                callback_data: `select_package:${pkg.id}`
            }]);

            await client.sendMessage(chatId, message, {
                reply_markup: {
                    inline_keyboard: inlineKeyboard
                }
            }, true);
        }

        return true;
    }

    // Показать статистику подписки пользователя
    async showSubscriptionStats(context) {
        const chatId = context.chatId;
        const client = context.client;
        const user = context.user;
        const userStorage = context.userStorage;

        // Получаем информацию о подписке пользователя
        const userSubscription = await userStorage.get('subscription', null);

        if (!userSubscription || !userSubscription.planId || !userSubscription.planName) {
            // Если у пользователя нет подписки
            await client.sendMessage(chatId, await l(user, "You do not have an active subscription. Choose a plan via the Subscription management menu"), {}, true);
            return true;
        }

        // Проверяем, не истекла ли подписка
        const now = new Date();
        if (userSubscription.endDate && new Date(userSubscription.endDate) < now) {
            await client.sendMessage(chatId, await l(user, "Your subscription has expired. Choose a new plan via the Subscription management menu"), {}, true);
            return true;
        }

        // Рассчитываем, сколько дней осталось до конца подписки
        const endDate = new Date(userSubscription.endDate);
        const daysLeft = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));

        // Получаем текущие показатели использования
        const usedToday = userSubscription.usedToday || 0;
        const dailyLimit = userSubscription.dailyLimit || 0;
        const bonusLimit = userSubscription.bonusLimit || 0;
        const extraToday = userSubscription.extraToday || 0;

        // Общий лимит на сегодня
        const totalLimit = dailyLimit + bonusLimit + extraToday;

        // Формируем сообщение
        let message = `📊 <b>${await l(user, "Your subscription statistics")}</b>\n\n`;
        message += `🔹 ${await l(user, `Plan:`) + ` ${userSubscription.planName}`}\n`;
        message += `🔹 ${await lp(user, `Today limit: {count} messages`, totalLimit)}\n`;
        message += `🔹 ${await lp(user, `Today used: {count} messages`, usedToday)}\n`;

        if (bonusLimit > 0) {
            message += `🔹 ${await lp(user, `Upgrade bonus: +{count} messages per day`, bonusLimit)}\n`;
        }

        if (extraToday > 0) {
            message += `🔹 ${await lp(user, `Extra messages: +{count} for today`, extraToday)}\n`;
        }

        message += `🔹 ${await lp(user, `Days left: {count}`, daysLeft)}\n`;
        message += `🔹 ${await l(user, `Subscription end date:`) + `${endDate.toLocaleDateString()}`}\n`;

        await client.sendMessage(chatId, message, {}, true);
        return true;
    }

    // Показать админ-статистику по подпискам
    async showAdminStats(context) {
        const chatId = context.chatId;
        const client = context.client;
        const user = context.user;
        const botStorage = context.botStorage;

        const today = new Date().toISOString().split('T')[0];
        const currentMonth = today.substring(0, 7); // YYYY-MM
        const currentYear = today.substring(0, 4);  // YYYY

        // Получаем все планы
        const plans = await botStorage.get('subscription.plans', []);

        // Получаем статистику активных подписок
        const activePlansStats = await botStorage.get('subscription.stats.activePlans', {});

        // Вычисляем общее количество активных подписок
        let totalActivePlans = 0;
        for (const planId in activePlansStats) {
            totalActivePlans += activePlansStats[planId] || 0;
        }

        // Получаем статистику доходов
        const dailyIncome = await botStorage.get(`subscription.stats.income.daily.${today}`, 0);
        const monthlyIncome = await botStorage.get(`subscription.stats.income.monthly.${currentMonth}`, 0);
        const yearlyIncome = await botStorage.get(`subscription.stats.income.yearly.${currentYear}`, 0);

        // Получаем статистику использования
        const messagesCount = await botStorage.get(`subscription.stats.usage.messages.${today}`, 0);
        const limitExceededCount = await botStorage.get(`subscription.stats.usage.limitExceeded.${today}`, 0);

        // Формируем список активных подписок по планам
        let activePlansMessage = '';
        for (const plan of plans) {
            const count = activePlansStats[plan.id] || 0;
            activePlansMessage += `    ${plan.name}: ${count}\n`;
        }

        // Формируем ТОП-3 популярных плана
        const topPlans = [...plans]
            .filter(p => activePlansStats[p.id] > 0)
            .sort((a, b) => (activePlansStats[b.id] || 0) - (activePlansStats[a.id] || 0))
            .slice(0, 3);

        let topPlansMessage = '';
        for (let i = 0; i < topPlans.length; i++) {
            const plan = topPlans[i];
            topPlansMessage += `    ${i + 1}. ${plan.name}: ${activePlansStats[plan.id] || 0}\n`;
        }

        // Получаем статистику по пакетам сообщений
        const dailyPackagesStats = await botStorage.get(`subscription.stats.packages.daily.${today}`, {});
        const monthlyPackagesStats = await botStorage.get(`subscription.stats.packages.monthly.${currentMonth}`, {});
        const yearlyPackagesStats = await botStorage.get(`subscription.stats.packages.yearly.${currentYear}`, {});

        // Вычисляем общее количество купленных пакетов
        let dailyPackagesCount = 0;
        let monthlyPackagesCount = 0;
        let yearlyPackagesCount = 0;

        for (const packageId in dailyPackagesStats) {
            dailyPackagesCount += dailyPackagesStats[packageId] || 0;
        }

        for (const packageId in monthlyPackagesStats) {
            monthlyPackagesCount += monthlyPackagesStats[packageId] || 0;
        }

        for (const packageId in yearlyPackagesStats) {
            yearlyPackagesCount += yearlyPackagesStats[packageId] || 0;
        }

        // Формируем сообщение
        let message = `📊 <b>${await l(user, "Subscription Statistics")}</b>\n\n`;

        message += `👥 ${await lp(user, `Active subscribers: {count}`, totalActivePlans)}\n`;
        message += activePlansMessage + '\n';

        message += `🏆 ${await l(user, "Most Popular Plans:")}\n`;
        message += topPlansMessage + '\n';

        message += `💰 ${await l(user, "Income:")}\n`;
        message += `    ${await lp(user, `For today: {count} stars`, dailyIncome)}\n`;
        message += `    ${await lp(user, `For a month: {count} stars`, monthlyIncome)}\n`;
        message += `    ${await lp(user, `For a year: {count} stars`, yearlyIncome)}\n\n`;

        message += `📦 ${await l(user, "Purchased message packages:")}\n`;
        message += `    ${await l(user, `For today:`) + ` ${dailyPackagesCount}`}\n`;
        message += `    ${await l(user, `For a month:`) + ` ${monthlyPackagesCount}`}\n`;
        message += `    ${await l(user, `For a year:`) + ` ${yearlyPackagesCount}`}\n\n`;

        message += `📝 ${await l(user, "Usage:")}\n`;
        message += `    ${await l(user, `Messages today:`) + ` ${messagesCount}`}\n`;
        message += `    ${await lp(user, `Limit exceeded: {count} times`, limitExceededCount)}\n`;

        await client.sendMessage(chatId, message, {}, true);
        return true;
    }

    // Настройка планов подписки (для админа)
    async setupSubscriptionPlans(context) {
        const chatId = context.chatId;
        const client = context.client;
        const user = context.user;
        const botStorage = context.botStorage;

        // Получаем текущие планы и пакеты
        const plans = await botStorage.get('subscription.plans', []);
        const packages = await botStorage.get('subscription.messagePackages', []);

        let message = `⚙️ <b>${await l(user, "Subscription Settings")}</b>\n\n`;

        // Создаем клавиатуру с возможными действиями
        const inlineKeyboard = [
            [{
                text: await l(user, "Enable/Disable subscriptions"),
                callback_data: "subscription_toggle"
            }],
            [{
                text: await l(user, "Add plan"),
                callback_data: "subscription_add_plan"
            }],
            [{
                text: await l(user, "Remove plan"),
                callback_data: "subscription_remove_plan"
            }],
            [{
                text: await l(user, "Add message package"),
                callback_data: "subscription_add_package"
            }],
            [{
                text: await l(user, "Remove message package"),
                callback_data: "subscription_remove_package"
            }]
        ];

        // Текущий статус подписок
        const subscriptionEnabled = await botStorage.get('subscription.enabled', false);
        message += `🔹 ${await l(user, `Subscription status: ${subscriptionEnabled ? 'Enabled' : 'Disabled'}`)}\n\n`;

        // Список текущих планов
        message += `📝 ${await l(user, "Current plans:")}\n`;
        if (plans.length === 0) {
            message += `    ${await l(user, "There are no plans configured")}\n\n`;
        } else {
            for (const plan of plans) {
                message += `    📌 <b>${plan.name}</b> - ` + await lp(user, '{count} stars', plan.price) + `\n`;
                message += `        ${await lp(user, `Limit: {count} messages/day`, plan.messageLimit) + `, ` + await lp(user, 'Period: {count} days', plan.duration)}\n`;
            }
            message += '\n';
        }

        // Список текущих пакетов
        message += `📦 ${await l(user, "Current messages packets:")}\n`;
        if (packages.length === 0) {
            message += `    ${await l(user, "There are no packets configured")}\n`;
        } else {
            for (const pkg of packages) {
                message += `    📩 <b>${pkg.name}</b> - ` + await lp(user, '{count} stars', pkg.price) + `\n`;
                message += `        ${await l(user, `Messages:`) + ` +${pkg.messageCount}`}\n`;
            }
        }

        await client.sendMessage(chatId, message, {
            reply_markup: {
                inline_keyboard: inlineKeyboard
            }
        }, true);

        return true;
    }

    // Получение количество часов до сброса лимита
    getHoursUntilReset() {
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);

        const diffMs = tomorrow - now;
        return Math.ceil(diffMs / (1000 * 60 * 60));
    }

    /**
     * Show user selection menu for manual assignment
     */
    async showUserSelectionMenu(context, offset = 0) {
        const {client, chatId, user, bot} = context;
        const limit = 15;

        try {
            // Get total count for pagination info
            const totalCount = await ExternalUser.count({
                where: {
                    botId: bot.id
                }
            });

            if (totalCount === 0) {
                await client.sendMessage(chatId, await l(user, "No users found for this bot."), {}, true);
                return true;
            }

            // Get users for current page
            const users = await ExternalUser.findAll({
                where: {
                    botId: bot.id
                },
                attributes: ['id', 'externalId', 'firstName', 'lastName', 'username'],
                order: [['firstName', 'ASC']],
                limit: limit,
                offset: offset
            });

            if (users.length === 0 && offset > 0) {
                // If no users on this page but offset > 0, go back to first page
                return await this.showUserSelectionMenu(context, 0);
            }

            // Create inline keyboard with users
            const inlineKeyboard = [];

            // Add user buttons
            for (const externalUser of users) {
                const displayName = externalUser.username
                    ? `@${externalUser.username}`
                    : `${externalUser.firstName}${externalUser.lastName ? ' ' + externalUser.lastName : ''}`;

                inlineKeyboard.push([{
                    text: `ID: ${externalUser.externalId} | ${displayName}`,
                    callback_data: `sub_assign_user_${externalUser.id}`
                }]);
            }

            // Add pagination navigation buttons
            const navigationButtons = [];

            // Previous button
            if (offset > 0) {
                const prevOffset = Math.max(0, offset - limit);
                navigationButtons.push({
                    text: '⬅️ Previous',
                    callback_data: `sub_assign_prev_${prevOffset}`
                });
            }

            // Next button
            if (offset + limit < totalCount) {
                const nextOffset = offset + limit;
                navigationButtons.push({
                    text: 'Next ➡️',
                    callback_data: `sub_assign_next_${nextOffset}`
                });
            }

            if (navigationButtons.length > 0) {
                inlineKeyboard.push(navigationButtons);
            }

            // Add back button
            inlineKeyboard.push([{
                text: '⬅️ Back to Admin Menu',
                callback_data: 'subscription_admin'
            }]);

            const pageInfo = Math.floor(offset / limit) + 1;
            const totalPages = Math.ceil(totalCount / limit);

            await client.sendMessage(chatId,
                await l(user, `Select a user to assign subscription:\n\nPage ${pageInfo} of ${totalPages} (${totalCount} total users)`), {
                    reply_markup: {
                        inline_keyboard: inlineKeyboard
                    }
                }, true);

            return true;
        } catch (error) {
            log.error('Error showing user selection menu', {
                error: error.message,
                botId: bot.id
            }, 'showUserSelectionMenu', 'SubscriptionHandlerTool');

            await client.sendMessage(chatId, await l(user, "An error occurred while loading users. Please try again."), {}, true);
            return true;
        }
    }

    /**
     * Show plan selection menu for manual assignment
     */
    async showPlanSelectionMenu(context, userId) {
        const {client, chatId, user, botStorage} = context;

        try {
            // Get the selected user info
            const selectedUser = await ExternalUser.findByPk(userId);
            if (!selectedUser) {
                await client.sendMessage(chatId, await l(user, "User not found. Please try again."), {}, true);
                return true;
            }

            // Get available plans
            const plans = await botStorage.get('subscription.plans', []);

            if (plans.length === 0) {
                await client.sendMessage(chatId, await l(user, "No subscription plans available. Please create plans first."), {}, true);
                return true;
            }

            // Create inline keyboard with plans
            const inlineKeyboard = [];

            for (const plan of plans) {
                const planText = `${plan.name} - ${plan.price} ⭐ (${plan.messageLimit} msg/day, ${plan.duration} days)`;
                inlineKeyboard.push([{
                    text: planText,
                    callback_data: `sub_assign_plan_${plan.id}_${userId}`
                }]);
            }

            // Add back button
            inlineKeyboard.push([{
                text: '⬅️ Back to User Selection',
                callback_data: 'subscription_manual_assignment'
            }]);

            const displayName = selectedUser.username
                ? `@${selectedUser.username}`
                : `${selectedUser.firstName}${selectedUser.lastName ? ' ' + selectedUser.lastName : ''}`;

            await client.sendMessage(chatId,
                await l(user, `Select a subscription plan to assign to:\n${displayName} (ID: ${selectedUser.id.substring(0, 8)}...)`), {
                    reply_markup: {
                        inline_keyboard: inlineKeyboard
                    }
                }, true);

            return true;
        } catch (error) {
            log.error('Error showing plan selection menu', {
                error: error.message,
                userId: userId
            }, 'showPlanSelectionMenu', 'SubscriptionHandlerTool');

            await client.sendMessage(chatId, await l(user, "An error occurred while loading plans. Please try again."), {}, true);
            return true;
        }
    }

    /**
     * Assign subscription to user manually
     */
    async assignSubscriptionToUser(context, userId, planId) {
        const {client, chatId, user, botStorage} = context;

        try {
            // Get the selected user
            const selectedUser = await ExternalUser.findByPk(userId);
            if (!selectedUser) {
                await client.sendMessage(chatId, await l(user, "User not found. Please try again."), {}, true);
                return true;
            }

            // Get the selected plan
            const plans = await botStorage.get('subscription.plans', []);
            const plan = plans.find(p => p.id === planId);

            if (!plan) {
                await client.sendMessage(chatId, await l(user, "Plan not found. Please try again."), {}, true);
                return true;
            }

            // Get user storage for the selected user
            const targetUserStorage = getStorage('ExternalUser', selectedUser.id);

            // Get current subscription to determine extension logic
            const currentSubscription = await targetUserStorage.get('subscription', null);

            // Calculate start and end dates
            const startDate = new Date();
            let endDate = new Date();

            // Extend for 30 days from current end date or from now if no active subscription
            if (currentSubscription && currentSubscription.endDate && new Date(currentSubscription.endDate) > new Date()) {
                // Extend from current end date
                endDate = new Date(currentSubscription.endDate);
                endDate.setDate(endDate.getDate() + 30);
            } else {
                // Start from now and add 30 days
                endDate.setDate(endDate.getDate() + 30);
            }

            // Create new subscription following the same pattern as processPurchase
            const newSubscription = {
                planId: plan.id,
                planName: plan.name,
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
                dailyLimit: plan.messageLimit,
                bonusLimit: 0,
                usedToday: 0,
                extraToday: 0,
                lastUsedDate: new Date().toISOString().split('T')[0]
            };

            // Save the subscription
            await targetUserStorage.set('subscription', newSubscription);

            // Update statistics (following business:5 payment type pattern)
            await this.updateSubscriptionStats(botStorage, plan.id, 0); // 0 price for manual assignment

            // Send confirmation to owner
            const displayName = selectedUser.username
                ? `@${selectedUser.username}`
                : `${selectedUser.firstName}${selectedUser.lastName ? ' ' + selectedUser.lastName : ''}`;

            await client.sendMessage(chatId,
                await l(user, `✅ Subscription successfully assigned!\n\n` +
                    `User: ${displayName}\n` +
                    `Plan: ${plan.name}\n` +
                    `Duration: 30 days\n` +
                    `Daily limit: ${plan.messageLimit} messages\n` +
                    `Valid until: ${endDate.toLocaleDateString()}`), {}, true);

            // Optionally notify the user (if they have interacted with the bot)
            try {
                await client.sendMessage(selectedUser.externalId,
                    await l(selectedUser, `🎉 You have been granted a subscription!\n\n` +
                        `Plan: ${plan.name}\n` +
                        `Duration: 30 days\n` +
                        `Daily limit: ${plan.messageLimit} messages\n` +
                        `Valid until: ${endDate.toLocaleDateString()}\n\n` +
                        `Enjoy using the bot!`), {}, true);
            } catch (notifyError) {
                // If we can't notify the user, that's okay - just log it
                log.info('Could not notify user about subscription assignment', {
                    userId: selectedUser.id,
                    externalId: selectedUser.externalId,
                    error: notifyError.message
                }, 'assignSubscriptionToUser', 'SubscriptionHandlerTool');
            }

            return true;
        } catch (error) {
            log.error('Error assigning subscription to user', {
                error: error.message,
                userId: userId,
                planId: planId
            }, 'assignSubscriptionToUser', 'SubscriptionHandlerTool');

            await client.sendMessage(chatId, await l(user, "An error occurred while assigning the subscription. Please try again."), {}, true);
            return true;
        }
    }
}

export default SubscriptionHandlerTool;

